/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        bulk_mem_copy.h
 * brief       High_performance memory copy
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30ju<PERSON>25, <PERSON>, Create the file.
 * 
 */
 
 #include <stddef.h>

/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               FUNCTIONS                                      */
/************************************************************************************************/

/*
 *fn           int bulk_mem_copy(unsigned char *to, unsigned char *from, int len) 
 *brief        Modify the memcpy().
 *
 * param[in]   to    Target memory address
 * param[in]   from  Source Target memory address
 * param[in]   len   the length of the copied bytes
 * 
 * return      wrong number
*/
 int bulk_mem_copy(unsigned char *to, unsigned char *from, int len);