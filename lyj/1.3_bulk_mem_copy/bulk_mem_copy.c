/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        bulk_mem_copy.c
 * brief       High_performance memory copy
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30ju<PERSON>25, <PERSON>, Create the file.
 * 
 */
 
 #include <stddef.h>
/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/
    
/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/*
 *fn           int bulk_mem_copy(unsigned char *to, unsigned char *from, int len) 
 *brief        Modify the memcpy().
 *
 * param[in]   to    Target memory address
 * param[in]   from  Source Target memory address
 * param[in]   len   the length of the copied bytes
 * 
 * return      wrong number
*/
int bulk_mem_copy(unsigned char *to, unsigned char *from, int len){
    /*Parameter checking*/
    if (!to || !from || len <= 0){  
        return -1;
    }
    /*Check for memory overlap situations*/
    /*If the memory is overlapping and the source address is before the target address, 
    then it is necessary to copy from the back to the front*/
    if (to > from && to < from + len){ 
        for (int i = len - 1; i >= 0;i--){
            to[i] = from[i];
        }
    }
    /*If there is no memory overlap or the source address is after the destination address, 
    then the data can be copied from the front to the back*/
    else{  
        /*Use word-length copying to improve efficiency*/
        size_t word_size = sizeof(unsigned long);
        unsigned long *dst_word = (unsigned long *)to;
        unsigned long *src_word = (unsigned long *)from;

        int word_len = len / word_size;
        for (int i = 0; i < word_len; i++){
            dst_word[i] = src_word[i];
        }

        int remaining = len % word_size;
        unsigned char *dst_byte = to + word_len * word_size;
        unsigned char *src_byte = from + word_len * word_size;
        for (int i = 0; i < remaining; i++){
            dst_byte[i] = src_byte[i];
        }
    }
    return 0;
}
/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/
