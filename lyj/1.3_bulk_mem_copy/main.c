/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        main.c
 * brief       High_performance memory copy
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30ju<PERSON>25, <PERSON>, Create the file.
 * 
 */
 
 #include <stdio.h>
 #include <string.h>
 #include "bulk_mem_copy.h"
/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/
int main(){
    /*Test1: Ordinary memory copy*/
    char src1[] = "hello world!";
    char dest1[20] = {0};
    bulk_mem_copy((unsigned char *)dest1, (unsigned char *)src1, strlen(src1)+1);
    printf("Test1: %s\n", dest1);

    /*Test2: Memory overlap (The source address is before the destination address)*/
    char buf2[30] = "A test for overlap copy";
    bulk_mem_copy((unsigned char *)(buf2 + 5), (unsigned char *)buf2, 15);
    printf("Test2: %s\n", buf2 + 5);

    /*Test3: Memory overlap (The source address is after the destination address)*/
    char buf3[30] = "Another test for overlap copy";
    bulk_mem_copy((unsigned char *)buf3, (unsigned char *)(buf3) + 8, 10);
    printf("Test3: %s\n", buf3);
    
    /*Test4: Error parameter check*/
    int ret = bulk_mem_copy(NULL, (unsigned char *)src1, 10);
    printf("Test4: %s\n", ret == -1 ? "Error handled" : "Error not handled");

    return 0;
}