/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd. *  
* file        client.c
* brief       TCP communication - client.
*
* author      <PERSON>
* version     1.0.0
* date        07August25
*
* history     \arg 1.0.0, 07<PERSON><PERSON><PERSON>25, <PERSON>, Create the file.
*
*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <pthread.h>

/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

#define SERVER_IP "***************"    /*Replace with server's actual IP*/
#define PORT 8080
#define BUFFER_SIZE 1024

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

int sock;     /*Socket descriptor for server connection*/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/*
 *fn           void *receive_handler(void *arg)
 *brief        Thread function to handle incoming messages continuously receives messages from 
               server and prints them.
 *
 * param[in]   arg    A positive integer
 * 
 */
void *receive_handler(void *arg){
    char buffer[BUFFER_SIZE];
    while(1){
        int bytes_received = recv(sock, buffer, BUFFER_SIZE, 0);
        if (bytes_received <=0){
            perror("connection closed");
            exit(EXIT_FAILURE);
        }
        buffer[bytes_received] = '\0';
        printf("server: %s", buffer);
    }
    return NULL;
}

/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/

int main(){
    struct sockaddr_in serv_addr;

    /*Create client socket*/
    if((sock = socket(AF_INET, SOCK_STREAM, 0)) < 0){
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }

    /*Configure server address*/
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(PORT);

    /*Convert IP address string to binary form*/
    if(inet_pton(AF_INET, SERVER_IP, &serv_addr.sin_addr) <= 0){
        perror("invalid address");
        exit(EXIT_FAILURE);
    }

    /*Connect to server*/
    if(connect(sock, (struct  sockaddr*)&serv_addr, sizeof(serv_addr)) < 0){
        perror("connection failed");
        exit(EXIT_FAILURE);
    }

    printf("connected to server\n");

    /*Create receive thread*/
    pthread_t recv_thread;
    pthread_create(&recv_thread, NULL, receive_handler, NULL);

    /*Main thread: message sending loop*/
    char message[BUFFER_SIZE];

        while(1){
        fgets(message, BUFFER_SIZE, stdin);
        if(send(sock, message, strlen(message),0) < 0){
            perror("send failed");
            break;
        }
    }

    /*Cleanup resources*/
    close(sock);
    return 0;
}
