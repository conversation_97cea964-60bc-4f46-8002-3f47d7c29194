/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd. *  
* file        mychardev.c
* brief       Character device driver with case conversion capability.
*
* author      <PERSON>
* version     1.0.0
* date        07August25
*
* history     \arg 1.0.0, 07August25, <PERSON>, Create the file.
*
*/

#include <linux/module.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/slab.h>
#include <linux/uaccess.h>
#include <linux/ctype.h> 

#define DEVICE_NAME "mychardev"     /*Device name*/
static int cap = 0;                 /*Case conversion flag (0 = disable, 1 = enable)*/
module_param(cap, int, S_IRUGO);    /*Declare module parameter*/
MODULE_PARM_DESC(cap, "Enable uppercase conversion (1=enable)");  /*Parameter description*/

static dev_t dev_num;             /*Device number (major + minor)*/
static struct cdev my_cdev;       /*Character device structure*/
static struct class *my_class;    /*Device class pointer*/
static struct device *my_device;  /*Device node pointer*/

/*Device open function*/
static int mychardev_open(struct inode *inodep, struct file *filep) { 
    return 0; 
}

/*Device release function*/
static int mychardev_release(struct inode *inodep, struct file *filep) { 
    return 0; 
}

/*Device write operation handler*/
static ssize_t mychardev_write(struct file *filp, const char __user *buf, size_t count, loff_t *f_pos) {
    char *kernel_buf;
    int i;
    
    /*Validate write size doesn't exceed page size limit*/
    if (count > PAGE_SIZE) 
        return -EFBIG;
    
    /*Allocate kernel buffer*/
    kernel_buf = kmalloc(count + 1, GFP_KERNEL);
    if (!kernel_buf) 
        return -ENOMEM;
    
    /*Copy data from user-space to kernel-space*/
    if (copy_from_user(kernel_buf, buf, count)) {
        kfree(kernel_buf);
        return -EFAULT;
    }
    kernel_buf[count] = '\0'; 

    /*Apply uppercase conversion if enabled*/
    if (cap) {
        for (i = 0; i < count; i++) 
            kernel_buf[i] = toupper(kernel_buf[i]); 
    }
    
    /*Print kernel log message*/
    printk(KERN_INFO "MYCHARDEV [cap=%d]: %s\n", cap, kernel_buf);
    kfree(kernel_buf);
    return count;
}

/*File operations structure*/
static struct file_operations fops = {
    .owner = THIS_MODULE,
    .open = mychardev_open,
    .release = mychardev_release,
    .write = mychardev_write,
};

/*Module initialization function*/
static int __init mychardev_init(void) {
    
    /*Dynamically allocate character device numbers*/
    int ret = alloc_chrdev_region(&dev_num, 0, 1, DEVICE_NAME);
    if (ret < 0) {
        printk(KERN_ERR "Failed to allocate device number: %d\n", ret);
        return ret;
    }
    
    /*Initialize character device structure with file operations*/
    cdev_init(&my_cdev, &fops);

    /*Add character device to the system*/
    ret = cdev_add(&my_cdev, dev_num, 1);
    if (ret < 0) {
        unregister_chrdev_region(dev_num, 1);
        printk(KERN_ERR "Failed to add cdev: %d\n", ret);
        return ret;
    }
    
    /*Create device class*/
    my_class = class_create(THIS_MODULE, "mychardev_class");
    if (IS_ERR(my_class)) {
        ret = PTR_ERR(my_class);
        cdev_del(&my_cdev);
        unregister_chrdev_region(dev_num, 1);
        printk(KERN_ERR "Failed to create class: %d\n", ret);
        return ret;
    }
    
    /*Create device node*/
    my_device = device_create(my_class, NULL, dev_num, NULL, DEVICE_NAME);
    if (IS_ERR(my_device)) {
        ret = PTR_ERR(my_device);
        class_destroy(my_class);
        cdev_del(&my_cdev);
        unregister_chrdev_region(dev_num, 1);
        printk(KERN_ERR "Failed to create device: %d\n", ret);
        return ret;
    }
    
    printk(KERN_INFO "MYCHARDEV loaded. Major=%d, cap=%d\n", MAJOR(dev_num), cap);
    return 0;
}

/*Module exit function*/
static void __exit mychardev_exit(void) {
    device_destroy(my_class, dev_num);
    class_destroy(my_class);
    cdev_del(&my_cdev);
    unregister_chrdev_region(dev_num, 1);
    printk(KERN_INFO "MYCHARDEV unloaded\n");
}

/*Register module initialization and exit functions*/
module_init(mychardev_init);
module_exit(mychardev_exit);
MODULE_LICENSE("GPL");