/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        main.c
 * brief       Replace the pattern 101 with 011 in the binary code of a 32-bit integer from high
 *             to low.
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30ju<PERSON><PERSON>, <PERSON>, Create the file.
 * 
 */
 
#include <stdio.h>
#include "Bit_pattern_substitution.h"
/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/
int main(){
    unsigned int num;
    printf("Enter a 32-bit integer in hexadecimal(e.g., 0XA0000000):");
    scanf("%x", &num);

    unsigned int result = pattern_replace(num);
    printf("Original: 0X%08X\n", num);
    printf("Result:   0X%08X\n", result);

    return 0;
}