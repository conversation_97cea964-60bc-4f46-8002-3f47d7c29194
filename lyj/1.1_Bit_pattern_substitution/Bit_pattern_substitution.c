/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        Bit_pattern_substitution.c
 * brief       Replace the pattern 101 with 011 in the binary code of a 32-bit integer from high
 *             to low.
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30<PERSON><PERSON><PERSON>, <PERSON>, Create the file.
 * 
 */
 
 #include <stdio.h>
 #include "Bit_pattern_substitution.h"
/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/
#define DET 0XE0000000  /*Check three figures*/
#define MAT 0XA0000000  /*The 101 pattern to be matched*/
#define REP 0X60000000  /*The 011 pattern to be replaced*/
#define NUM 30          /*Only 30 bits need to be checked, as each check involves three bits*/
/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/*
 *fn           unsigned int pattern_replace(unsigned int input)
 *brief        Replace the pattern 101 with 011 in the binary code of a 32-bit integer from high
 *             to low.
 *
 * param[in]   input  Original 32-bit integer
 * param[out]  input  The 32-bit integer after pattern replacement
 * 
 * return      The integer value after mode replacement
*/
unsigned int pattern_replace(unsigned int input){
    unsigned int mask = DET;
    unsigned int pattern = MAT;
    unsigned int replacement = REP;
    
    for (int i = 0; i < NUM; i++){
        /*If the 101 mode is detected, 
        then clear the original three digits and replace them with new value*/
        if ((input & mask) == pattern){  
            input = (input & ~mask) | replacement;
            i += 2;
            mask >>= 3;
            pattern >>= 3;
            replacement >>= 3;
        }
        else{
            mask >>= 1;
            pattern >>= 1;
            replacement >>= 1;
        }
    }

    return input;
}
/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/
