 /* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        Prime_number_enumeration.h
 * brief       Input a positive integer n, find the set of all prime numbers that are less than 
 *             or equal to n, and output them in rows of 10.
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30<PERSON><PERSON><PERSON>, <PERSON>, Create the file.
 * 
 */
 
 #include <stdio.h>
 #include <stdbool.h>
 #include <math.h>

/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               FUNCTIONS                                      */
/************************************************************************************************/
/*
 *fn           bool is_prime(int num)
 *brief        Determine whether The input value is a prime number.
 *
 * param[in]   num    A positive integer
 * 
 * return      Boolean value
*/
 bool is_prime(int num);

 /*
 *fn           void print_primes(int primes[], int count)
 *brief        Format and output prime numbers.
 *
 * param[in]   primes[]  Array of prime numbers
 * param[in]   count     The number of digits contained within the primes[]
 * 
*/
 void print_primes(int primes[], int count);

