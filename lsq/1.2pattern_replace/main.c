#include<stdio.h>

/*................FUNCTIONS................*/
unsigned int pattern_replace(unsigned int input_int)
{
    unsigned int result = 0;
    unsigned int mask = 7,find5 = 5,turn3 = 3,temp = input_int;
    int highest_bit = 1;

    if(input_int == 0)
    {
        return 0;
    }

    while (temp >>= 1)
    {
        highest_bit++;
    }
    

    for(int i = highest_bit-3;i >= 0;)
    {
        unsigned int current_3 = (input_int >> i)&mask;
        if(current_3 == find5)
        {
            current_3 = turn3;
            result |= (current_3<<i);
            i -= 3;
        }
        else
        {
            current_3 >>= 2;
            result |= (current_3 << (i+2));
            i -= 1;
        }
        
    }
    return result;
}
int main(){
    unsigned int num;
    printf("Please enter a number:");
    scanf("%d", &num);

    unsigned int result0=pattern_replace(num);
    printf("%dturn:%d",num,result0);
}
