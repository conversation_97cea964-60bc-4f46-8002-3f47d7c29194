#include <linux/init.h>
#include <linux/module.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/uaccess.h>

static int cap = 0;
module_param(cap, int, 0644);
MODULE_PARM_DESC(cap, "1=Uppercase letters,0=lowercase letters");

static dev_t dev_num;
static struct cdev my_cdev;
static char kbuf[512];

/*
*\fn        static ssize_t my_write(struct file *filp, const char __user *buf, size_t count, loff_t *f_pos)
*\brief     实现字符设备的写操作，接收用户数据并根据参数决定是否需要转换大小写
*\param     filp 指向与设备关联的文件结构体
*\param     buf 用户空间缓冲区指针，存储写入设备的数据
*\param     count 写入的字节数
*\param     f_pos 文件当前读写位置的指针
*
*\return    count
*\retval    实际写入的字节数
*
*\note      防止缓冲区溢出
*/

static ssize_t my_write(struct file *filp, const char __user *buf,
                        size_t count, loff_t *f_pos)
{
    int i;
    count = count > 511 ? 511 : count;
    if (copy_from_user(kbuf, buf, count))
    {
        return -EFAULT;
    }
    kbuf[count] = '\0';

    if (cap)
    {
        for (i = 0; i < count; i++)
        {
            if(kbuf[i] >= 'a' && kbuf[i] <= 'z')
            {
                kbuf[i] -= 32;
            }
        }
    }
    printk(KERN_INFO "receive: %s\n", kbuf);
    return count;
}



static const struct file_operations fops = {
    .owner = THIS_MODULE,
    .write = my_write,
};

/*
*\fn        static __init int char_int(void)
*\brief     模块初始化函数，完成设备号分配和字符设备注册
*
*\return    成功返回0，失败返回-1
*/

static __init int char_int(void)
{
    if(alloc_chrdev_region(&dev_num, 0, 1, "simple_char") < 0)
    {
        return -1;
    }

    cdev_init(&my_cdev, &fops);

    if(cdev_add(&my_cdev, dev_num, 1) < 0)
    {
        unregister_chrdev_region(dev_num, 1);
        return -1;
    }

    printk(KERN_INFO "The module loads successfully:Master device number=%d, cap=%d\n", MAJOR(dev_num), cap);
    return 0;
}

/*
*\fn        static __exit void char_exit(void)
*\brief     模块卸载函数，释放设备号和字符设备
*/

static __exit void char_exit(void)
{
    cdev_del(&my_cdev);
    unregister_chrdev_region(dev_num, 1);
    printk(KERN_INFO "The module was successfully uninstalled\n");
}

module_init(char_int);
module_exit(char_exit);
MODULE_LICENSE("GPL");
MODULE_DESCRIPTION("Character_device");