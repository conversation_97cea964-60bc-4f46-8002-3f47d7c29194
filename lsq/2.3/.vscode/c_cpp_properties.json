{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "/usr/src/linux-headers-6.8.0-65-generic/include/**", "/usr/src/linux-headers-6.8.0-65-generic/include/linux/**", "/usr/src/linux-headers-6.8.0-65-generic/arch/x86/include/**", "/usr/src/linux-headers-6.8.0-65-generic/arch/x86/include/generated/**"], "defines": ["__KERNEL__", "MODULE"], "compilerPath": "/usr/bin/gcc", "cStandard": "c17", "cppStandard": "gnu++17", "intelliSenseMode": "linux-gcc-x64"}], "version": 4}