#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <signal.h>
#include <fcntl.h>

#define MAX_CMD_LEN 1000
#define MAX_AGRS 64
#define READ_END 0
#define WRITE_END 1

/*
*\fn        void tilde_expand(char *cmd)
*\brief     将命令中的～字符替换成用户主目录
*\details   遍历命令字符串查找~字符，若存在则把~字符替换成用户主目录，
*           并保留~前后的字符
*\param     cmd 指向命令字符串的指针
*
*\return    无返回值
*\retval    无显式返回值，函数直接修改cmd
*
*\note      解析后的命令长度不能超过MAX_CMD_LEN，否则可能导致缓冲区溢出
*/

void tilde_expand(char *cmd)
{
    char *tilde_pos = strchr(cmd, '~');
    if (tilde_pos == NULL)
    {
        return;
    }

    char *home = getenv("HOME");
    if(home == NULL)
    {
        return;
    }

    char new_cmd[MAX_CMD_LEN];

    strncpy(new_cmd, cmd, tilde_pos - cmd);
    new_cmd[tilde_pos - cmd] = '\0';
    strcat(new_cmd, home);
    strcat(new_cmd, tilde_pos + 1);
    strcpy(cmd, new_cmd);
}

/*
*\fn        void split_cmd(char *cmd, char **args)
*\brief     把命令字符串分割成参数数组，适配execvp函数要求
*\details   以空格为分隔符，把命令字符串拆分成多个参数存储到args数组中，
            在数组末尾添加NULL作为结束标志来符合execvp函数的要求
*\param     cmd 指向命令字符串的指针，分割后原字符串会被修改
*\param     args 指向字符指针数组的指针，用于存储分割后的参数
*
*\return    无返回值
*\retval    无显式返回值
*
*\note      参数数量超过MAX_AGRS - 1时，超出的部分会被忽略
*/

void split_cmd(char *cmd, char **args)
{
    int i = 0;
    char *token = strtok(cmd, " ");
    while (token != NULL && i < MAX_AGRS - 1)
    {
        args[i++] = token;
        token = strtok(NULL, " ");
    }
    args[i] = NULL;
}


/*
*\fn        int main()
*\brief     实现一个简单的类似 Shell 的程序,可以在程序中输入命令
            （要求编写的另一个程序）的路径并在子进程中执行 
            在调用命令时可以将参数传给命令 
            可以从命令中输入内容，发送给简单 Shell 接收，并打印出来。 
            如果简易 Shell 收到的内容为 stop，则 Shell 将命令进程关闭。 
            直接在简易 Shell 中输入 exit，简易 Shell 自己退出
*
*\return    返回0表示正常退出
*
*/

int main()
{
    char cmd[MAX_CMD_LEN];
    char *args[MAX_AGRS];
    pid_t child_pid = -1;
    int pipe_fds[2];
    int status;

    while (1)
    {
        printf("simple-shell> ");
        fflush(stdout);
        if (fgets(cmd, MAX_CMD_LEN, stdin) == NULL)
        {
            printf("\n");
            break;
        }

        cmd[strcspn(cmd, "\n")] = '\0';
        tilde_expand(cmd);

        if (strcmp(cmd, "exit") == 0)
        {
            printf("quit\n");
            if(child_pid != -1)
            {
                kill(child_pid, SIGTERM);
                waitpid(child_pid, NULL, 0);
            }
            break;
        }

        if (strcmp(cmd, "stop") == 0)
        {
            if (child_pid != -1)
            {
                kill(child_pid, SIGTERM);
                waitpid(child_pid, &status, 0);
                child_pid = -1;
                printf("The sub-program has been terminated\n");
            }
            else
            {
                printf("No running child processes\n");
            }
            continue;
        }

        if (pipe(pipe_fds) == -1)
        {
            perror("pipe");
            continue;
        }


        child_pid = fork();
        if (child_pid == -1)
        {
            perror("fork");
            close(pipe_fds[READ_END]);
            close(pipe_fds[WRITE_END]);
            continue;
        }
        else if (child_pid == 0)
        {
            close(pipe_fds[WRITE_END]);
            dup2(pipe_fds[READ_END], STDIN_FILENO);
            close(pipe_fds[READ_END]);

            split_cmd(cmd, args);
            execvp(args[0], args);
            perror("execvp");
            exit(EXIT_FAILURE);
        }
        else
        {
            close(pipe_fds[READ_END]);
            char input_buf[MAX_CMD_LEN];
            int stdin_fds = fileno(stdin);
            fcntl(stdin_fds, F_SETFL, O_NONBLOCK);
            while (1)
            {
                if(waitpid(child_pid, &status, WNOHANG) != 0)
                {
                    child_pid = -1;
                    break;
                }
                ssize_t n = read(stdin_fds, input_buf, MAX_CMD_LEN - 1);
                if(n > 0)
                {
                    input_buf[n] = '\0';
                    input_buf[strcspn(input_buf, "\n")] = '\0';
                    if(strcmp(input_buf,"stop") == 0)
                    {
                        kill(child_pid, SIGTERM);
                        waitpid(child_pid, &status, 0);
                        child_pid = -1;
                        break;
                    }
                    write(pipe_fds[WRITE_END], input_buf, strlen(input_buf));
                    write(pipe_fds[WRITE_END], "\n", 1);
            
                }
                else if (n == -1)
                {
                    usleep(10000);
                }

                /*if (fgets(input_buf, MAX_CMD_LEN, stdin) == NULL)
                {
                    printf("\n");
                    break;
                }
                input_buf[strcspn(input_buf, "\n")] = '\0';

                if (strcmp(input_buf, "stop") == 0)
                {
                    kill(child_pid, SIGTERM);
                    waitpid(child_pid, NULL, 0);
                    child_pid = -1;
                    break;
                }

                if (write(pipe_fds[WRITE_END], input_buf, strlen(input_buf) == -1))
                {
                    perror("write");
                    break;
                }

                write(pipe_fds[WRITE_END], "\n", 1);*/
                
            }

            close(pipe_fds[WRITE_END]);
            if(child_pid != -1)
            {
                waitpid(child_pid, &status, 0);
                child_pid = -1;
            }
            fcntl(stdin_fds, F_SETFL, 0);
        }
    }

    return 0;

}
