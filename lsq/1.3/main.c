#include<stdio.h>
#include<stdbool.h>

/*................FUNCTIONS................*/
bool prime_is(int num)
{
    if(num == 2)
    {
        return true;
    }
    if(num%2 == 0)
    {
        return false;
    }
    for(int i = 3; i * i <= num; i += 2)
    {
        if(num%i == 0)
        {
            return false;
        }
    }
    return true;

}
int main()
{
    printf("Please enter a number between 2 and 1000:\n");

    int number;
    int count = 0;

    scanf("%d",&number);
    if(number < 2 || number > 1000)
    {
        printf("Error!Please enter a number between 2 and 1000.\n");
    }
    else
    {
        for (int i = 2; i <= number; i++)
        {
            if(prime_is(i))
            {
                printf("%4d",i);
                count++;
                if(count%10 == 0)
                {
                    printf("\n");
                }
                else
                {
                    printf(",");
                }
            }
            
        }
        
    }
    return 0;
}
