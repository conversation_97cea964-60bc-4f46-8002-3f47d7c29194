#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>
#include <pthread.h>
#include <arpa/inet.h>

#define PORT 6666
#define BUFFER_SIZE 1024

/*
*\fn        void *msg_recv(void *arg)
*\brief     作为线程函数，负责循环接收服务器信息并打印，接收失败或服务器断开时退出
*\param     arg 指向与服务器连接的套接字文件描述符
*
*\return    无返回值
*\retval    无显式返回值
*/

void *msg_recv(void *arg)
{
    int sock_fd = *(int *)arg;
    char buffer[BUFFER_SIZE];
    ssize_t len;

    while (1)
    {
        memset(buffer, 0, BUFFER_SIZE);
        len = recv(sock_fd, buffer, BUFFER_SIZE - 1, 0);
        if (len <= 0)
        {
            if(len < 0)
            {
                perror("recv failed");
            }
            else
            {
                printf("server is disconnected\n");
            }
            break;
        }
        printf("server:%s\n", buffer);
    }
    close(sock_fd);
    exit(0);
}

int main(int argc, char *argv[])
{
    if (argc != 2)
    {
        fprintf(stderr, "%s <server IP address>\n", argv[0]);
        exit(EXIT_FAILURE);
    }

    int sock_fd;
    struct sockaddr_in server_addr;
    pthread_t recv_thread;

    if ((sock_fd = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);

    if (inet_pton(AF_INET, argv[1], &server_addr.sin_addr) <= 0)
    {
        perror("invaild address not supported");
        close(sock_fd);
        exit(EXIT_FAILURE);
    }

    if (connect(sock_fd, (struct sockaddr *)&server_addr, 
            sizeof(server_addr)) == -1)
    {
        perror("connection failed");
        close(sock_fd);
        exit(EXIT_FAILURE);
    }

    printf("Connected to the server %s:%d\n", argv[1], PORT);

    if (pthread_create(&recv_thread, NULL, msg_recv, &sock_fd) != 0)
    {
        perror("pthread_create failed");
        close(sock_fd);
        exit(EXIT_FAILURE);
    }

    pthread_detach(recv_thread);

    char buffer[BUFFER_SIZE];
    while (1)
    {
        memset(buffer, 0, BUFFER_SIZE);
        if (fgets(buffer, BUFFER_SIZE - 1, stdin) == NULL)
        {
            perror("fgets failed");
            break;
        }

        buffer[strcspn(buffer, "\n")] = '\0';

        if (send(sock_fd, buffer, strlen(buffer), 0) == -1)
        {
            perror("send failed");
            break;
        }
    }
    close(sock_fd);
    return 0;
}