#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>
#include <pthread.h>
#include <arpa/inet.h>

#define PORT 6666
#define BUFFER_SIZE 1024

/*
*\fn        void *msg_recv(void *arg)
*\brief     作为线程函数，负责循环接收客户端信息并打印，接收失败或客户端断开时退出
*\param     arg 指向客户端连接的套接字文件描述符
*
*\return    无返回值
*\retval    无显式返回值
*/

void *msg_recv(void *arg)
{
    int client_fd = *(int *)arg;
    char buffer[BUFFER_SIZE];
    ssize_t len;

    while (1)
    {
        memset(buffer, 0, BUFFER_SIZE);
        len = recv(client_fd, buffer, BUFFER_SIZE - 1, 0);
        if (len <= 0)
        {
            if(len < 0)
            {
                perror("recv failed");
            }
            else
            {
                printf("client is disconnected\n");
            }
            break;
        }
        printf("client:%s\n", buffer);
    }
    close(client_fd);
    exit(0);
}

int main()
{
    int server_fd, client_fd;
    struct sockaddr_in server_addr, client_addr;
    socklen_t client_len = sizeof(client_addr);
    pthread_t recv_thread;

    if ((server_fd = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(PORT);

    if (bind(server_fd,(struct sockaddr *)&server_addr,
            sizeof(server_addr)) == -1)
    {
        perror("bind failed");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    if (listen(server_fd, 5) == -1)
    {
        perror("listen failed");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    printf("The server starts, waiting for the client to connect...\n");

    if ((client_fd = accept(server_fd, (struct sockaddr *)
            &client_addr,&client_len)) == -1)
    {
        perror("accept failed");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    printf("client %s:%d is connected\n", inet_ntoa(client_addr.sin_addr),
           ntohs(client_addr.sin_port));

    if (pthread_create(&recv_thread, NULL, msg_recv, &client_fd) != 0)
    {
        perror("pthread_create failed");
        close(client_fd);
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    pthread_detach(recv_thread);

    char buffer[BUFFER_SIZE];
    while (1)
    {
        memset(buffer, 0, BUFFER_SIZE);
        if (fgets(buffer, BUFFER_SIZE - 1, stdin) == NULL)
        {
            perror("fgets failed");
            break;
        }

        buffer[strcspn(buffer, "\n")] = '\0';

        if (send(client_fd, buffer, strlen(buffer), 0) == -1)
        {
            perror("send failed");
            break;
        }
    }
    close(client_fd);
    close(server_fd);
    return 0;
}
