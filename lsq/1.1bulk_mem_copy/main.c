#include<stdio.h>
#include<assert.h>

/*................FUNCTIONS................*/
int bulk_mem_copy(unsigned char *to,unsigned char *from,int len)
{
    if(len <= 0)
    {
        return 0;
    }

    /*Determine if the memory overlaps*/
    if(to < from && to + len > from)
    {
        /*to is before from, and it is copied from back to front*/
        unsigned char *end_to = to + len -1;
        unsigned char *end_from = from + len -1;
        while (len--)
        {
            *end_to-- = *end_from--;
        }
        
    }
    else
    {
         while (len--)
        {
            *to++ = *from++;
        }
    }
    return 1;
}

void test_no_overlap()
{
    unsigned char from[10]={1,2,3,4,5,6,7,8,9,10};
    unsigned char to[10]={0};
    int len = 5;

    bulk_mem_copy(to,from,len);
    for(int i = 0; i < len; i++)
    {
        assert(to[i] == from[i]);
    }
    printf("test_no_overlap passed.\n");
}


void test_to_before_from_partial_overlap()
{
    unsigned char data[10] = {1,2,3,4,5,6,7,8,9,10};
    unsigned char *from = data + 2;
    unsigned char *to = data;
    int len = 5;

    bulk_mem_copy(to,from,len);
    for(int i = 2;i < 2 + len; i++)
    {
        assert(data[i] == data[i-2]);
    }
    printf("test_to_before_from_partial_overlap passed.\n");
}


void test_to_and_from_overlap_completely()
{
    unsigned char data[10] = {1,2,3,4,5,6,7,8,9,10};
    unsigned char *from = data;
    unsigned char *to = data;
    int len = 5;

    bulk_mem_copy(to,from,len);
    for(int i = 0; i < len; i++)
    {
        assert(data[i] == data[i]);
    }
    printf("test_to_and_from_overlap_completely passed.\n");
}


void test_to_after_from_partial_overlap()
{
    unsigned char data[10] = {1,2,3,4,5,6,7,8,9,10};
    unsigned char *from = data;
    unsigned char *to = data+2;
    int len = 5;

    bulk_mem_copy(to,from,len);
    for(int i = 2; i < 2 + len; i++)
    {
        assert(data[i] == data[i-2]);
    }
    printf("test_to_after_from_partial_overlap passed.\n");
}


void test_len_zero()
{
    unsigned char from[10] = {1,2,3,4,5,6,7,8,9,10};
    unsigned char to[10] = {0};
    int len = 0;

    bulk_mem_copy(to,from,len);
    for(int i = 0; i < len; i++)
    {
        assert(to[i] == from[i]);
    }
    printf("test_len_zero passed.\n");
}
int main()
{
    test_no_overlap();
    test_to_before_from_partial_overlap();
    test_to_and_from_overlap_completely();
    test_to_after_from_partial_overlap();
    test_len_zero();
    return 0;
}
