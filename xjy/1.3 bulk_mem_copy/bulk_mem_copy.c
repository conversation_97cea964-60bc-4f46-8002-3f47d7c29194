/*  Copyright(c) 2009-2025 Shenzhen TP-LINK Technologies Co.Ltd.
 *
 * file     bulk_mem_copy.c
 * brief    A funciton that can copy sth with 2 pointers and a length, also give some test cases.
 * 
 * author   <PERSON><PERSON> Jiaying
 * version  1.0.0
 * date     31Jul25 
 *  
 * history  \arg 1.0.0, 31Jul25, <PERSON><PERSON>, Create the file.
 */

#include <stdio.h>
#include <string.h>

/**************************************************************************************************/
/*                                        DEFINES                                                */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        TYPES                                                   */
/**************************************************************************************************/
#define BITS_CHAR 8  /*bits of char*/

/**************************************************************************************************/
/*                                        EXTERN_PROTOTYPES                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_PROTOTYPES                                        */
/**************************************************************************************************/
int bulk_men_copy(unsigned char *to, unsigned char *from, int len);
int test_display(unsigned char *string, unsigned char *to, unsigned char *from, int len);

/**************************************************************************************************/
/*                                        VARIABLES                                               */
/**************************************************************************************************/
char string_1[] = "AAAAAAAAaaaaaaaaBBBBBBBBbbbbbbbbCCCCCCCCccccccccDDDDDDDDdddddddd";
char *from_1 = &string_1[0];   /*test1*/
char *to_1 = &string_1[16];
int len_1 = 31;
char string_2[] = "AAAAAAAAaaaaaaaaBBBBBBBBbbbbbbbbCCCCCCCCccccccccDDDDDDDDdddddddd";
char *from_2 = &string_2[16];  /*test2*/
char *to_2 = &string_2[0];
int len_2 = 30;
char string_3[] = "AAAAAAAAaaaaaaaaBBBBBBBBbbbbbbbbCCCCCCCCccccccccDDDDDDDDdddddddd";
char *from_3 = &string_3[16];  /*test3*/
char *to_3 = &string_3[0];
int len_3 = 0;
char string_4[] = "AAAAAAAAaaaaaaaaBBBBBBBBbbbbbbbbCCCCCCCCccccccccDDDDDDDDdddddddd";
char *from_4 = &string_4[16];  /*test4*/
char *to_4 = &string_4[16];
int len_4 = 30;
char string_5[] = "AAAAAAAAaaaaaaaaBBBBBBBBbbbbbbbbCCCCCCCCccccccccDDDDDDDDdddddddd";
char *from_5 = NULL;           /*test5*/
char *to_5 = &string_5[16];
int len_5 = 30;
char string_6[] = "AAAAAAAAaaaaaaaaBBBBBBBBbbbbbbbbCCCCCCCCccccccccDDDDDDDDdddddddd";
char *from_6 = &string_5[0];   /*test6*/
char *to_6 = &string_5[16];
int len_6 = -1;

/**************************************************************************************************/
/*                                        LOCAL_FUNCTIONS                                         */
/**************************************************************************************************/
/*
 * fn         int bulk_men_copy(unsigned char *to, unsigned char *from, int len);
 * brief      Copy len bytes from the points *from to the points *to.
 * details    Considering the overlapping parts, copy the memory from right to left when the *from 
 *            at the left side of *to. Similarly, copy the memory from left to right when the *from 
 *            at the right side of *to. Besides, depending on the size of len, move multiple bytes 
 *            each time when the size is big. The number of moving bytes can be divided to 16, 8, 
 *            4, 1. This method can speed up copying.
 * 
 * param[in]  *to    The target position of copying memory.
 * param[in]  *from  The beginning of copying memory.
 * param[in]  len    The bytes of copying memory.
 * 
 * return     0
 * retval     Successful execution.
 * 
 * note       Don't input the NULL points, the len should be positive.
 */
int bulk_men_copy(unsigned char *to, unsigned char *from, int len)
{
    int chunks_128 = len / 16;         /*the number of 16bytes chunks*/
    int chunks_64 = (len % 16) / 8;    /*the number of 8bytes chunks*/
    int chunks_32 = (len % 8) / 4;     /*the number of 4bytes chunks*/
    int rem = len % 4;                 /*the number of 1bytes chunks*/
    __uint128_t* to_128 = (__uint128_t*)to;        /*pointer *to 16bytes*/
    __uint128_t* from_128 = (__uint128_t*)from;    /*pointer *from 16bytes*/
    __uint64_t* to_64 = (__uint64_t*)to;           /*pointer *to 8bytes*/
    __uint64_t* from_64 = (__uint64_t*)from;       /*pointer *from 8bytes*/
    __uint32_t* to_32 = (__uint32_t*)to;           /*pointer *to 4bytes*/
    __uint32_t* from_32 = (__uint32_t*)from;       /*pointer *from 4bytes*/
    __uint8_t* to_8 = (__uint8_t*)to;              /*pointer *to 1byte*/
    __uint8_t* from_8 = (__uint8_t*)from;          /*pointer *from 1byte*/

    if((NULL == to) || (NULL == from) || (0 > len))  /*check the input*/
    {
        printf("error!");
        return 0;
    }

    if((to == from) || (0 == len))  /*don't need to copy*/
    {
        return 0;
    }

    if(from < to)  /*right to left*/
    {
        to_8 = (__uint8_t*)to + len;
        from_8 = (__uint8_t*)from + len; 
        while(rem--)
        {
            *(--to_8) = *(--from_8);
        }

        to_32 = (__uint32_t*)to_8;
        from_32 = (__uint32_t*)from_8;
        while(chunks_32--)
        {
            *(--to_32) = *(--from_32);
        } 

        to_64 = (__uint64_t*)to_32;
        from_64 = (__uint64_t*)from_32;
        while(chunks_64--)
        {
            *(--to_64) = *(--from_64);
        } 

        to_128 = (__uint128_t*)to_64;
        from_128 = (__uint128_t*)from_64;
        while(chunks_128--)
        {
            *(--to_128) = *(--from_128);
        }   
    }
    else  /*left to right*/
    {
        while(chunks_128--)
        {
            *(to_128++) = *(from_128++);
        }

        to_64 = (__uint64_t*)to_128;
        from_64 = (__uint64_t*)from_128; 
        while(chunks_64--)
        {
            *(to_64++) = *(from_64++);
        }

        to_32 = (__uint32_t*)to_64;
        from_32 = (__uint32_t*)from_64; 
        while(chunks_32--)
        {
            *(to_32++) = *(from_32++);
        }

        to_8 = (__uint8_t*)to_32;
        from_8 = (__uint8_t*)from_32; 
        while(rem--)
        {
            *(to_8++) = *(from_8++);
        }
    }
    return 0;
}

/*
 * fn         int test_display(unsigned char *string, unsigned char *to, 
                               unsigned char *from, int len);
 * brief      Display the copying result.
 * details    Input the string, pointer *to, pointer *from and len, use function 
 *            bulk_men_copy(to, from, len) to copy the memory. Print the changed string to compare 
 *            with the unchanged string.
 * 
 * param[in]  *string  The string to display.
 * param[in]  *to      The target position of copying memory.
 * param[in]  *from    The beginning of copying memory.
 * param[in]  len      The bytes of copying memory.
 * 
 * return     0
 * retval     Successful execution.
 * 
 * note       Don't input the NULL points, the len should be positive.
 */
int test_display(unsigned char *string, unsigned char *to, unsigned char *from, int len)
{
    if((NULL == to) || (NULL == from) || (0 > len))  /*check the input*/
    {
        printf("error!\n\n");
        return 0;
    }
    printf("from: %c1\n", *from);
    printf("to:   %c1\n", *to);
    printf("len:  %d\n", len);
    printf("string:  ");
    for(int i = 0; i < sizeof(string) * BITS_CHAR; i++)
    {
        printf("%c" , string[i]);
    }
    printf("\n");
    bulk_men_copy(to, from, len);
    printf("changed: ");
    for(int i = 0; i < sizeof(string) * BITS_CHAR; i++)
    {
        printf("%c" , string[i]);
    }
    printf("\n\n");
    return 0;
}

/**************************************************************************************************/
/*                                        PUBLIC_FUNCTIONS                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        GLOBAL_FUNCTIONS                                        */
/**************************************************************************************************/
/*
 * fn         int main()
 * brief      Test the fuction bulk_mem_copy(*to, *from ,len) and display the result.
 * details    Input 6 test cases, display the pointer *from, *to, the len bytes need to copy, the 
 *            string and the changed string. If the input has problem, it will give the tips.
 * 
 * return     0
 * retval     Successful execution.
 * 
 * note       This is a main function.
 */
int main()
{
    test_display(string_1, to_1, from_1, len_1);
    test_display(string_2, to_2, from_2, len_2);
    test_display(string_3, to_3, from_3, len_3);
    test_display(string_4, to_4, from_4, len_4);
    test_display(string_5, to_5, from_5, len_5);
    test_display(string_6, to_6, from_6, len_6);
    return 0;
}
