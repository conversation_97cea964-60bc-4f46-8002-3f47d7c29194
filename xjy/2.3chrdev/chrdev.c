#include <linux/module.h>   
#include <linux/kernel.h>   
#include <linux/init.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/uaccess.h>
#include <linux/slab.h>
#include <linux/ctype.h>


#define DEVICE_NAME "chrdev"


static int cap = 0;
module_param(cap, int, 0644);
MODULE_PARM_DESC(cap, "convert input to uppercase(1=enable, 0=disable)");


static dev_t dev_num;
static struct cdev my_cdev;
static struct class *my_class;
static struct device *my_device;


static ssize_t my_write(struct file *plip, const char __user *buf, size_t count, loff_t *f_pos)
{
	char *kernel_buf;
	size_t i;

	kernel_buf = kmalloc(count + 1, GFP_KERNEL);
	if(!kernel_buf)
	{
		return -ENOMEM;
	}

	if(copy_from_user(kernel_buf, buf, count))
	{
		kfree(kernel_buf);
		return -EFAULT;
	}

	kernel_buf[count] = '\0';

	if(cap)
	{
		for(i = 0; i < count; i++)
		{
			kernel_buf[i] = toupper((unsigned char)kernel_buf[i]);
		}
	}

	printk("receive %zu bytes: '%s'\n", count, kernel_buf);

	kfree(kernel_buf);
	return count;
}


static const struct file_operations fops = 
{
	.owner = THIS_MODULE,
	.write = my_write,
};


static int __init chrdev_module_init(void)   
{
	/*1. alloc*/
	if(alloc_chrdev_region(&dev_num, 0, 1, DEVICE_NAME) < 0)
	{
		printk(KERN_ERR "failed to alloc!");
		return -1;
	}

	/*2. init cdev*/
	cdev_init(&my_cdev, &fops);
	my_cdev.owner = THIS_MODULE;

	/*3. add cdev*/
	if(cdev_add(&my_cdev, dev_num, 1) < 0)
	{
		unregister_chrdev_region(dev_num, 1);
		printk(KERN_ERR "failed to add");
		return -1;
	}

	/*4. create class*/
	my_class = class_create(THIS_MODULE, "my_char_class");
	if(IS_ERR(my_class))
	{
		cdev_del(&my_cdev);
		unregister_chrdev_region(dev_num, 1);
		printk(KERN_ERR "failed to create class\n");
		return PTR_ERR(my_class);
	}

	/*5. create device*/
	my_device = device_create(my_class, NULL, dev_num, NULL, DEVICE_NAME);
	if(IS_ERR(my_device))
	{
		class_destroy(my_class);
		cdev_del(&my_cdev);
		unregister_chrdev_region(dev_num, 1);
		printk(KERN_ERR "failed to create device\n");
		return PTR_ERR(my_device);
	}

	printk("module loaded. device major-%d minor=%d\n", MAJOR(dev_num), MINOR(dev_num)); 
	return 0;
}

static void __exit chrdev_module_exit(void)
{
	device_destroy(my_class, dev_num);
	class_destroy(my_class);
	cdev_del(&my_cdev);
	unregister_chrdev_region(dev_num, 1);
	printk("module unloaded\n");
}

module_init(chrdev_module_init);
module_exit(chrdev_module_exit);

MODULE_LICENSE("GPL");  