/*  Copyright(c) 2009-2025 Shenzhen TP-LINK Technologies Co.Ltd.
 *
 * file     client.c
 * brief    socket about client
 * 
 * author   <PERSON><PERSON>
 * version  1.0.0
 * date     05Aug25 
 *  
 * history  \arg 1.0.0, 05Aug25, <PERSON><PERSON>aying, Create the file.
 */

#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <arpa/inet.h>
#include <unistd.h>

/**************************************************************************************************/
/*                                        DEFINES                                                 */
/**************************************************************************************************/
#define BUFF_SIZE 1024
#define PORT 10004

/**************************************************************************************************/
/*                                        TYPES                                                   */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        EXTERN_PROTOTYPES                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_PROTOTYPES                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        VARIABLES                                               */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_FUNCTIONS                                         */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        PUBLIC_FUNCTIONS                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        GLOBAL_FUNCTIONS                                        */
/**************************************************************************************************/
/*
 * fn         int main();
 * brief      easyshell
 * details
 * 
 * param[in]  argc      number of arg
 * param[in]  *argv[]   contain server's ip address
 * 
 * return     0
 * retval     successful execution
 * 
 */
int main(int argc,char *argv[])
{
    int sockfd;
    struct sockaddr_in serv_addr;

	char buff[BUFF_SIZE] = {0};  /*sent and receive message*/ 
	int byte_recv = 0;

    if(argc != 2)
    {
        printf("client <ipaddress> \n");
        exit(0);
    }
    char *serv_ip = argv[1];


    sockfd = socket(AF_INET,SOCK_STREAM,0);
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(PORT);

    inet_pton(AF_INET, serv_ip, &serv_addr.sin_addr);

    if((connect(sockfd, (struct sockaddr*)&serv_addr, sizeof(serv_addr))) < 0 )
    {
        printf("connect error %s errno: %d\n", strerror(errno), errno);
        exit(0);
    }
    printf("connected to server\n\n");


	while(1)
	{
        memset(buff, 0, BUFF_SIZE);
		printf("send msg: ");
        fgets(buff, BUFF_SIZE, stdin);
        if((send(sockfd, buff, strlen(buff), 0)) < 0)
        {
            printf("send msg error: %s errno : %d",strerror(errno),errno);
            exit(0);
        }

		if((strstr(buff, "quit") != NULL) && (buff[0] == 'q'))
        {
            printf("\nclient exiting...\n");
            send(sockfd, buff, strlen(buff), 0);
            break;
        }

        memset(buff, 0, BUFF_SIZE);
		byte_recv = recv(sockfd, buff, BUFF_SIZE, 0); 
        if(byte_recv > 0)
        {
            printf("received: %s\n", buff);
        }
        else if(byte_recv == 0)
        {
            printf("\ndisconneted\n");
            break;
        }
        else
        {
            printf("recv failed\n");
            break;
        }
	}

	close(sockfd);
	return 0;
}