/*  Copyright(c) 2009-2025 Shenzhen TP-LINK Technologies Co.Ltd.
 *
 * file     server.c
 * brief    socket about server
 * 
 * author   <PERSON><PERSON>
 * version  1.0.0
 * date     05Aug25 
 *  
 * history  \arg 1.0.0, 05Aug25, <PERSON><PERSON>aying, Create the file.
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <errno.h>
#include <unistd.h>
#include <arpa/inet.h>

/**************************************************************************************************/
/*                                        DEFINES                                                 */
/**************************************************************************************************/
#define BUFF_SIZE 1024
#define PORT 10004

/**************************************************************************************************/
/*                                        TYPES                                                   */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        EXTERN_PROTOTYPES                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_PROTOTYPES                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        VARIABLES                                               */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_FUNCTIONS                                         */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        PUBLIC_FUNCTIONS                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        GLOBAL_FUNCTIONS                                        */
/**************************************************************************************************/
/*
 * fn         int main();
 * brief      server
 * details
 * 
 * 
 * return     0
 * retval     successful execution
 * 
 */
int main()
{
    int serv_fd = 0;
    int clie_fd = 0;
    struct sockaddr_in sock_addr;

    char buff[BUFF_SIZE];   /*receive and send message*/
    int bytes_recv;        

    serv_fd = socket(AF_INET,SOCK_STREAM,0);

    memset(&sock_addr,0,sizeof(sock_addr));
    sock_addr.sin_family = AF_INET;
    sock_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    sock_addr.sin_port = htons(PORT);


    bind(serv_fd,(struct sockaddr *) &sock_addr, sizeof(sock_addr));
    listen(serv_fd, BUFF_SIZE);

    printf("Please wait for the client.\n");

    if((clie_fd = accept(serv_fd, (struct sockaddr*)NULL, NULL)) == -1)
    {
        printf("accpet socket error: %s errno :%d\n",strerror(errno),errno);
    }
    printf("client connected\n");

    while(1)
    {
        memset(buff, 0, BUFF_SIZE);

        bytes_recv = recv(clie_fd, buff, BUFF_SIZE, 0);
        if((strstr(buff, "quit") != NULL) && (buff[0] == 'q'))
        {
            printf("\ndisconnected\n");
            break;
        }
        if(bytes_recv > 0)
        {
            printf("\nreceived: %s", buff);
        }
        else if(bytes_recv == 0)
        {
            printf("\ndisconneted\n");
            break;
        }
        else
        {
            perror("recv failed");
            break;
        }
        

        memset(buff, 0, BUFF_SIZE);
        printf("send msg: ");
        fgets(buff, BUFF_SIZE, stdin);
        if((strstr(buff, "quit") != NULL) && (buff[0] == 'q'))
        {
            printf("\nserver exiting...\n");
            break;
        }
        if(send(clie_fd, buff, strlen(buff), 0) < 0)
        {
            perror("send failed");
            break;
        }
        
    }

    close(serv_fd);
    close(clie_fd);
    return 0;
}