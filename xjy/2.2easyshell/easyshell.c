/*  Copyright(c) 2009-2025 Shenzhen TP-LINK Technologies Co.Ltd.
 *
 * file     easyshell.c
 * brief    An easy program similar to shell
 * 
 * author   <PERSON><PERSON>
 * version  1.0.0
 * date     05Aug25 
 *  
 * history  \arg 1.0.0, 05Aug25, <PERSON><PERSON>ay<PERSON>, Create the file.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/wait.h>
#include <sys/select.h>
#include <signal.h>
#include <fcntl.h>
#include <ctype.h>
#include <errno.h>

/**************************************************************************************************/
/*                                        DEFINES                                                 */
/**************************************************************************************************/
#define MAX_INPUT 1024
#define MAX_ARGS 64

/**************************************************************************************************/
/*                                        TYPES                                                   */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        EXTERN_PROTOTYPES                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_PROTOTYPES                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        VARIABLES                                               */
/**************************************************************************************************/
volatile pid_t running_child = -1;
int to_child[2]={-1, -1};
int to_father[2]={-1, -1};

/**************************************************************************************************/
/*                                        LOCAL_FUNCTIONS                                         */
/**************************************************************************************************/
/*
 * fn         int exit_detect(char **args);
 * brief      detect exit
 * details
 * 
 * param[in]  **args  command
 * 
 * return     0
 * retval     successful execution
 * 
 * note       
 */
int exit_detect(char **args)
{
    if(strcmp(args[0], "exit") == 0)
    {
        if(0 < running_child)
        {
            kill(running_child, SIGTERM);
            waitpid(running_child, NULL,0);
        }
        exit(0);
    }
    return 0;
}


/*
 * fn         void set_nonblocking(int fd)
 * brief      set nonclocking
 * details
 * 
 * param[in]  int fd    the fd need to set
 *      
 */
void set_nonblocking(int fd)
{
    int flags = fcntl(fd, F_GETFL, 0);
    fcntl(fd, F_SETFL, flags | O_NONBLOCK);
}


/*
 * fn         void execute_command(char **args)
 * brief      execute the command
 * details
 * 
 * param[in]  **args   command
 * 
 * 
 */
void execute_command(char **args)
{
    if(pipe(to_child) == -1)
    {
        perror("pipe1 failed");
        return;
    }
    if(pipe(to_father) == -1)
    {
        perror("pipe2 failed");
        return;
    }

    pid_t pid = fork();

    if(pid == 0)    /*child*/
    {
        close(to_child[1]);  
        close(to_father[0]);  
        

        if(-1 == dup2(to_child[0], STDIN_FILENO))
        {
            perror("dup2 input to child failed");
            exit(1);
        }

        if(-1 == dup2(to_father[1], STDOUT_FILENO))  
        {
            perror("dup2 output to father failed");
            exit(1);
        }
        if(-1 == dup2(to_father[1], STDERR_FILENO))  
        {
            perror("dup2 stderr");
            exit(1);
        }
        close(to_child[0]);
        close(to_father[1]);

        if(strstr(*args, "stop") != NULL)
        {
            printf("no running child\n");
        }
        else
        {
            execvp(args[0], args);
            perror("execvp failed");
        }
       
        exit(1);
    }
    else if(0 < pid)  /*father*/
    {
        close(to_child[0]);  
        close(to_father[1]);  
        running_child = pid;

        set_nonblocking(STDIN_FILENO);
        set_nonblocking(to_father[0]);
        
        process_input();

        close(to_child[1]);  
        close(to_father[0]);  
        
        kill(pid, SIGTERM);
        waitpid(running_child, NULL, WNOHANG); 
    }
    else
    {
        perror("fork failed");
        close(to_child[0]);
        close(to_child[1]);
    }
}


/*
 * fn         void stop_child();
 * brief      stop child
 * details
 * 
 */
void stop_child()
{
    if(running_child == -1)
    {
        printf("no running child\n");
        return;
    }
    // printf("stop child now...\n");
    if(kill(running_child, SIGTERM) == -1)
    {
        perror("stop failed\n");
    }
    else
    {
        printf("\n");
    }
}


/*
 * fn         void process_input()
 * brief      watch the input and output
 * details
 * 
 */
void process_input()
{
    fd_set readfds;
    struct timeval tv;
    int max_fd;


    while(running_child != -1)
    { 
        /*watch input*/
        FD_SET(STDIN_FILENO, &readfds);
        max_fd = STDIN_FILENO;

        /*watch to_father*/
        FD_SET(to_father[0], &readfds);
        if(to_father[0] > max_fd)
        {
            max_fd = to_father[0];
        }

        /*timeout set*/
        tv.tv_sec = 0;
        tv.tv_usec = 100000;

        int retval = select(max_fd + 1, &readfds, NULL, NULL, &tv);

        
        if(retval == -1)
        {
            perror("select fault");
            break;
        }
        else
        {
            if(FD_ISSET(to_father[0], &readfds)) /*sth out*/
            {
                char output[1024];
                ssize_t count = read(to_father[0], output, sizeof(output) -1);
                if(count > 0)
                {
                    output[count] = '\0';
                    printf("%s", output);
                }
                else if(count == 0)
                {
                    printf("\n");
                    break;
                }
            }
            if(FD_ISSET(STDIN_FILENO, &readfds)) /*sth input*/
            {
                char input[1024];
                ssize_t count = read(STDIN_FILENO,input, sizeof(input) -1);

                if(count > 0)
                {
                    if(strstr(input, "stop") != NULL)
                    {
                        stop_child();
                        break;
                    }
                    input[count] = '\0';
                    write(to_child[1], input, count);
                }
                else if(count == 0)
                {
                    // stop_child();
                    break;
                }
                
            }
        }
        if(waitpid(running_child, NULL, WNOHANG) == running_child)
        {
            running_child = -1;
            break;
        }
    }
    close(to_child[1]);
    close(to_father[0]);
    int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, flags & ~O_NONBLOCK);
}


/**************************************************************************************************/
/*                                        PUBLIC_FUNCTIONS                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        GLOBAL_FUNCTIONS                                        */
/**************************************************************************************************/
/*
 * fn         int main();
 * brief      easyshell
 * details
 * 
 * 
 * return     0
 * retval     successful execution
 * 
 */
int main()
{
    char command[MAX_INPUT];
    char *args[MAX_ARGS];

    while(1)
    {
        printf("easy shell>");
        fflush(stdout);
        fgets(command, MAX_INPUT, stdin);

        command[strcspn(command, "\n")] = '\0';   /*remove the \n*/

        if(0 == strlen(command))  /*empty input*/
        {
            continue;
        }

        int i = 0;
        args[i] = strtok(command, " ");
        while(args[i] && i < MAX_ARGS - 1)
        {
            args[++i] = strtok(NULL, " ");
        }
        args[i] = NULL;

        exit_detect(args);
        execute_command(args);
    }
    if(running_child != -1)
    {
        printf("stop child\n");
        kill(running_child, SIGKILL);
    }

    return 0;
}