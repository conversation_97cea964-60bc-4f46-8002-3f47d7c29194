/*  Copyright(c) 2009-2025 Shenzhen TP-LINK Technologies Co.Ltd.
 *
 * file     pattern_replace.c
 * brief    Replace the 101 to 011 in the int num.
 * 
 * author   <PERSON><PERSON>
 * version  1.0.0
 * date     31Jul25 
 *  
 * history  \arg 1.0.0, 31J<PERSON>25, <PERSON><PERSON>, Create the file.
 */

#include <stdio.h>

/**************************************************************************************************/
/*                                        DEFINES                                                 */
/**************************************************************************************************/
#define BIT 1                /*the bit need to move in the next judjument*/
#define BITS 3               /*the bits need to move in the next judjument*/
#define MASK 0xe0000000      /*the mask to choose 3 bit, just like 11100000...*/
#define MASK_END 0x00000007  /*the mask in the end of the number*/
#define COND 0xa0000000      /*to find condition 101*/
#define REPLA 0x80000000     /*change to 011*/
#define BYTE 8               /*the bits of a byte*/

/**************************************************************************************************/
/*                                        TYPES                                                   */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        EXTERN_PROTOTYPES                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_PROTOTYPES                                        */
/**************************************************************************************************/
unsigned int pattern_replace(unsigned int input_int);

/**************************************************************************************************/
/*                                        VARIABLES                                               */
/**************************************************************************************************/
unsigned int input_num = 21;    /*input number*/
unsigned int output_num = 0;    /*output number*/

/**************************************************************************************************/
/*                                        LOCAL_FUNCTIONS                                         */
/**************************************************************************************************/
/*
 * fn         unsigned int pattern_replace(unsigned int input_int);
 * brief      Find pattern 101 in the input_int, and change to 011.
 * details    Choose 3 bits from high to low, judge if there is 101, if true, change to 011 and 
 *            move 3 bits, otherwise do nothing and move 1 bit to judge the next 3 bits.
 * 
 * param[in]  input_int  An int number need to change.
 * 
 * return     The int number after change.
 * retval     The pattern 101 in the input_int is all change to 011.
 * 
 * note       Please input the unsigned int number.
 */
unsigned int pattern_replace(unsigned int input_int)
{
    unsigned int mask = MASK;     /*the mask to choose 3 bit, just like 11100000...*/
    unsigned int cond = COND;     /*to find 101*/
    unsigned int repla = REPLA;   /*change to 011*/

    while(MASK_END <= mask)  /*from high to low, move 3 bits*/
    {
        if((input_int & mask ^ cond) == 0)  /*find 101*/
        {
            input_int = (input_int | mask) & (~repla);  /*change bits to 011*/
            mask = mask >> BITS;
            cond = cond >> BITS;
            repla = repla >> BITS;
        }
        else  /*keep moving*/
        {
            mask = mask >> BIT;
            cond = cond >> BIT;
            repla = repla >> BIT;
        }
        
    }

    return input_int;
}

/**************************************************************************************************/
/*                                       PUBLIC_FUNCTIONS                                         */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                       GLOBAL_FUNCTIONS                                         */
/**************************************************************************************************/
/*
 * fn         int main()
 * brief      Print the input number and change the pattern 101 to 011, output the changed number.
 * details    Print the int input_num first, then use function 'pattern_replace(input_int)' to 
 *            change the pattern 101 to 011 from high to low. Output the changed number in the end.
 * 
 * return     0
 * retval     Successful execution.
 * 
 * note       This is a main function. input_num can be changed.
 */
int main()
{
    printf("input_num:  %d\n", input_num);
    output_num = pattern_replace(input_num);
    printf("output_num: %d\n", output_num);
    return 0;
}