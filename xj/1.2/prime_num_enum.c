/*  Copyright(c) 2009-2025 Shenzhen TP-LINK Technologies Co.Ltd.
 *
 * file     prime_num_enum.c
 * brief    Find all primes not bigger than the input number.
 * 
 * author   <PERSON><PERSON>
 * version  1.0.0
 * date     31Jul25 
 *  
 * history  \arg 1.0.0, 31Jul25, <PERSON><PERSON>, Create the file.
 */

#include <stdio.h>

/**************************************************************************************************/
/*                                        DEFINES                                                 */
/**************************************************************************************************/
#define MAX_PRI_NUM 168  /*the max number of primes under 1000*/
#define NUM_LINE 10      /*the conuts of output number each line*/
#define MIN 2            /*the min of input*/
#define MAX 1000         /*the max of input*/

/**************************************************************************************************/
/*                                        TYPES                                                   */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        EXTERN_PROTOTYPES                                       */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        LOCAL_PROTOTYPES                                        */
/**************************************************************************************************/
unsigned int num_is_prime(unsigned int num);
unsigned int find_the_primes(unsigned int n);

/**************************************************************************************************/
/*                                        VARIABLES                                               */
/**************************************************************************************************/
unsigned int input_n = 0;               /*the input number*/
unsigned int prime[MAX_PRI_NUM] = {0};  /*an array to store the primes*/
unsigned int pri_counts = 0;            /*the counts of primes*/

/**************************************************************************************************/
/*                                        LOCAL_FUNCTIONS                                         */
/**************************************************************************************************/
/*
 * fn         unsigned int num_is_prime(unsigned int num);
 * brief      Judge if the input number is a prime.
 * details    First check if the number is 2(2 is a prime), if not, check if it can be divided 
 *            evenly by the number between 2 to square num(int number). Once find a number that 
 *            can divided evenly, return 0 to indicate it is not a prime, otherwise return 1 to 
 *            indicate it is a prime.
 * 
 * param[in]  num    The input number need to judge.
 * 
 * return     The judgement of prime.
 * retval     Return 1 if it is a prime and 0 if it is not a prime.
 * 
 * note       Please input the unsigned int number.
 */
unsigned int num_is_prime(unsigned int num)
{
    if(2 == num)  /*2 is a prime*/
    {
        return 1;
    }
    for(int i = 2; i * i <= num; i++)  /*judge the prime*/
    {
        if(num % i == 0)
        {
            return 0;
        }
    }
    return 1;
}

/*
 * fn         unsigned int find_the_primes(unsigned int n);
 * brief      Find all the primes not bigger than n.
 * details    Check the input number to find if it is between 2 and 1000. If not, print error tips. 
 *            Then judge each number between 2 and n by num_is_prime(num), record each prime to the 
 *            array prime[]. After that, print each prime. Every prime occupy 4 chars and the 
 *            counts of prime in every line is not bigger than 10.
 * 
 * param[in]  n  An int number.
 * 
 * return     0
 * retval     Successful execution.
 * 
 * note       Please input the unsigned int number between 2 and 1000.
 */
unsigned int find_the_primes(unsigned int n)
{
    if((MIN > n) || (MAX < n))  /*check the input*/
    {
        printf("Error! Please input the number between 2 and 1000!\n");
    }
    else
    {
        for(int i = 2; i <= n; i++)  /*judge each number*/
        {
            if(num_is_prime(i))
            {
                prime[pri_counts++] = i;
            }
        }
    }

    printf("The prime:");
    for(int i = 0; i < pri_counts; i++)  /*output each prime*/
    {
        if(i % NUM_LINE == 0)    /*change the line*/
        {
            printf("\n");
        }

        printf("%4d", prime[i]);
        if(i < pri_counts - 1)  /*not the last one*/
        {
            printf(",");
        }
    }
    printf("\n");
    
    return 0;
}

/**************************************************************************************************/
/*                                        PUBLIC_FUNCTIONS                                        */
/**************************************************************************************************/

/**************************************************************************************************/
/*                                        GLOBAL_FUNCTIONS                                        */
/**************************************************************************************************/
/*
 * fn         int main()
 * brief      Print the tips to input a number, find the primes and output.
 * details    First give the tips to input an int number between 2 and 1000, then use the function 
 *            find_the_primes(input_n) to find the primes not bigger than the input number and 
 *            print all the primes.
 * 
 * return     0
 * retval     Successful execution.
 * 
 * note       This is a main function.
 */
int main()
{
    printf("Input a positive integer between 2 and 1000: ");  /*input tips*/
    scanf("%d", &input_n);

    find_the_primes(input_n);  /*find the primes and output*/
}

