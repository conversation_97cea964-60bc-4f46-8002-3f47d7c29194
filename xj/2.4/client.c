#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <pthread.h>

#define SERVER_IP "127.0.0.1"       /* the Loopback Address */
#define PORT 8080                   /* the agreed port */
#define MAX_BUFFER_SIZE 1024        /* the max length of the buff*/

/*
func_name: receive_handler;
func: receive socket message in a thread;
input&ouput: NULL(as the parameter of pthread_create);
*/

void *receive_handler(void *arg) 
{
    int sock = *(int *)arg;
    char buffer[MAX_BUFFER_SIZE];
    
    while(1) 
    {
        /* define the receive buffer */
        memset(buffer, 0, MAX_BUFFER_SIZE);
        int bytes_received = recv(sock, buffer, MAX_BUFFER_SIZE, 0);
        if(bytes_received <= 0) {
            perror("Connection closed");
            break;
        }
        printf("Server: %s\n", buffer);
    }
    return NULL;
}


int main(int argc, char *argv[]) 
{
    int sock = 0;
    struct sockaddr_in serv_addr;
    
    /* define and allocate the socket */
    if ((sock = socket(AF_INET, SOCK_STREAM, 0)) < 0) 
    {
        perror("Socket creation error");
        return -1;
    }
    
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(PORT);
    
    if(inet_pton(AF_INET, SERVER_IP, &serv_addr.sin_addr) <= 0) 
    {
        perror("Invalid address");
        return -1;
    }
    
    /* create the connection to the server */
    if (connect(sock, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) 
    {
        perror("Connection failed");
        return -1;
    }
    printf("Connected to server!\n");

    /* create a thread to receive from the server */
    pthread_t recv_thread;
    pthread_create(&recv_thread, NULL, receive_handler, &sock);
    
    /* send the message to the server */
    char message[MAX_BUFFER_SIZE];
    while(1) 
    {
        printf("Client> ");

        fgets(message, MAX_BUFFER_SIZE, stdin);
        
        /* set the string tail */
        message[strcspn(message, "\n")] = 0; 
        
        /* set the exit signal */
        if(strcmp(message, "exit") == 0) break;
        
        send(sock, message, strlen(message), 0);
    }
    close(sock);
    return 0;
}
