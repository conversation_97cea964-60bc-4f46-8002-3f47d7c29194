#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <pthread.h>

#define PORT 8080               /* the agreed port */
#define MAX_BUFFER_SIZE 1024    /* the max length of the buff*/

/*
func_name: receive_handler;
func: receive socket message in a thread;
input&ouput: NULL(as the parameter of pthread_create);
*/

void *receive_handler(void *arg) 
{
    int sock = *(int *)arg;
    char buffer[MAX_BUFFER_SIZE];

    while(1) 
    {
        memset(buffer, 0, MAX_BUFFER_SIZE);
        int bytes_received = recv(sock, buffer, MAX_BUFFER_SIZE, 0);
        if(bytes_received <= 0) 
        {
            perror("Connection closed");
            break;
        }
        printf("Client: %s\n", buffer);
    }
    return NULL;
}

int main(int argc, char *argv[]) 
{
    int server_fd, client_socket;
    struct sockaddr_in address;
    int opt = 1;
    int addrlen = sizeof(address);
    
    /* define and allocate the socket */
    if ((server_fd = socket(AF_INET, SOCK_STREAM, 0)) == 0) 
    {
        perror("socket failed");
        exit(EXIT_FAILURE);
    }
    
    if (setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR | SO_REUSEPORT, &opt, sizeof(opt))) 
    {
        perror("setsockopt");
        exit(EXIT_FAILURE);
    }
    
    /* set the socket and bind */
    address.sin_family = AF_INET;
    address.sin_addr.s_addr = INADDR_ANY;
    address.sin_port = htons(PORT);
    
    if (bind(server_fd, (struct sockaddr *)&address, sizeof(address)) < 0) 
    {
        perror("bind failed");
        exit(EXIT_FAILURE);
    }
    
    /* listen the fixed port */
    if (listen(server_fd, 3) < 0) 
    {
        perror("listen");
        exit(EXIT_FAILURE);
    }
    printf("Server listening on port %d...\n", PORT);

    /* create the connection to the client */
    if ((client_socket = accept(server_fd, (struct sockaddr *)&address, (socklen_t*)&addrlen)) < 0) 
    {
        perror("accept");
        exit(EXIT_FAILURE);
    }
    printf("Client connected!\n");
    
    /* create a thread to receive from the client */
    pthread_t recv_thread;
    pthread_create(&recv_thread, NULL, receive_handler, &client_socket);
    
    /* send the message to the client */
    char message[MAX_BUFFER_SIZE];
    while(1) 
    {
        printf("Server> ");

        fgets(message, MAX_BUFFER_SIZE, stdin);

        /* set the string tail */
        message[strcspn(message, "\n")] = 0;

        /* set the exit signal */
        if(strcmp(message, "exit") == 0) break;

        send(client_socket, message, strlen(message), 0);
    }
    close(client_socket);
    close(server_fd);
    return 0;
}
