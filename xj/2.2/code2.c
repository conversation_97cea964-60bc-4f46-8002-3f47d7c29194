#include<stdio.h>
#include<stdlib.h>
#include<string.h>
#include<unistd.h>
#include<sys/types.h>
#include<sys/wait.h>
#include<sys/select.h>
#include<signal.h>
#include<errno.h>


#define MAX_ARGS 20				/* the max number of the input order */
#define MAX_LINE 1024			/* the max length of the input order */
pid_t current_child_pid = 0;	/* a global variable to record the PID of the child */

/*
*func name: execute_command
*function: run the external command in the child process
*input: the path of the external command
*output: the output of the running code
*/

void execute_command(char **args)
{
	pid_t pid = fork();

	if (pid == 0)
	{
		/* the child process */
		execvp(args[0], args);
		perror("execvp");
		exit(EXIT_FAILURE);
	}
	/* the parent process */
	else if (pid > 0)
	{
		current_child_pid = pid;
		int status;
		fd_set readfds;
		struct timeval timeout;

		while (1)
		{
			FD_ZERO(&readfds);
			FD_SET(0, &readfds);
			timeout.tv_sec = 0;
			timeout.tv_usec = 100000;

			int nready = select(1, &readfds, NULL, NULL, &timeout);
			if (nready == -1)
			{
				perror("select");
				break;
			}
			/* check the child pid */
			int ret = waitpid(pid, &status, WNOHANG);
			if (ret == pid)
			{
				current_child_pid = 0;
				break;
			}
			else if (ret == -1)
			{
				perror("waitpid");
				current_child_pid = 0;
				break;
			}

			if (FD_ISSET(0, &readfds))
			{
				char input[MAX_LINE];
				if (fgets(input, MAX_LINE, stdin) == NULL)
				{
					continue;
				}
				input[strcspn(input, "\n")] = '\0';
				if (strcspn(input, "stop") == 0)
				{
					kill(pid, SIGTERM);
					printf("Command stopped.\n");
				}
				else
				{
					printf("Ignoring command during execution: %s \n", input);
				}
			}
		}
		/* wait the child pid stop */
		if (current_child_pid != 0)
		{
			waitpid(pid, &status, 0);
			current_child_pid = 0;
		}
		else
		{
			perror("fork");
		}
	}
}



int main()
{
	char line[MAX_LINE];
	char *args[MAX_ARGS];
	char *token;
	
	while (1)
	{
		printf("myshell>");
		fflush(stdout);

		if (fgets(line, MAX_LINE, stdin) == NULL)
		{
			break;
		}

		/* set the string tail */
		line[strcspn(line, "\n")] = '\0';

		/* split the input command */
		int i = 0;
		token = strtok(line, " ");
		while (token != NULL && i < MAX_ARGS - 1)
		{
			args[i++] = token;
			token = strtok(NULL, " ");
		}
		args[i] = NULL;

		/* process the command */
		if (args[0] == NULL)
		{
			continue;
		}
		/* exit: kill the child pid and break */
		else if (strcmp(args[0], "exit") == 0)
		{
			if (current_child_pid != 0)
			{
				kill(current_child_pid, SIGTERM);
				waitpid(current_child_pid, NULL, 0);
				current_child_pid = 0;
			}
				break;
		}
		/* stop: kill the child pid and continue */
		else if (strcmp(args[0], "stop") == 0)
		{	
			if (current_child_pid != 0)
			{
				kill(current_child_pid, SIGTERM);
				printf("Stopped the current command.\n");
			}
			else
			{
				printf("No command is running.\n");
			}
			continue;
		}
		/* execute the external commands */
		execute_command(args);
	}
		return 0;
}


