#include<linux/module.h>
#include<linux/kernel.h>
#include<linux/fs.h>
#include<linux/cdev.h>
#include<linux/uaccess.h>
#include<linux/slab.h>

#define DEVICE_NAME "mychardev" 

static int cap = 0;		/* the parameter */
static int major;		/* the dev number */
static struct cdev my_cdev;

module_param(cap, int, S_IRUGO |S_IWUSR);									/* declare the module with parm */
MODULE_PARM_DESC(cap, "Enable uppercase conversion (1=enable, 0=disable)");	/* describe the parm in modinfo */


/* the upper change func */
static void to_uppercase(char *str, size_t len)
{
	int i = 0;
	for (i = 0; i < len; i++)
	{
		if (str[i] >= 'a' && str[i] <= 'z')
		{
			str[i] = str[i] - 'a' + 'A';
		}
	}
}

/* the write func at the input buf */
static ssize_t mychardev_write(struct file *filp, const char __user *buf, size_t count, loff_t *f_pos)
{
	/* memory allocation */
	char *kernel_buf = kmalloc(count + 1, GFP_KERNEL);
	if (!kernel_buf)
	{
		return -ENOMEM;
	}
	if (copy_from_user(kernel_buf, buf, count))
	{
		kfree(kernel_buf);
		return -EFAULT;
	}
	
	kernel_buf[count] = '\0';
	
	if (cap == 1)
	{
		to_uppercase(kernel_buf, count);
	}
	printk(KERN_INFO "mychardev:received: %s\n", kernel_buf);

	kfree(kernel_buf);
	return count;
}

/* define the finished file operations */
static struct file_operations fops = 
{
	.owner = THIS_MODULE,
	.write = mychardev_write
};

static int __init mychardev_init(void)
{
	dev_t dev;

	/* register the char device */
	if (alloc_chrdev_region(&dev, 0, 1, DEVICE_NAME) < 0)
	{
		printk(KERN_ERR "Failed to allocate device number\n");
		return -1;
	}
	major = MAJOR(dev);

	/* Initialize the cdev struct */
	cdev_init(&my_cdev, &fops);
	if (cdev_add(&my_cdev, dev, 1) < 0)
	{
		unregister_chrdev_region(dev, 1);
		printk(KERN_ERR "Failed to add character device\n");
		return -1;
	}

	printk(KERN_INFO "mychardev: loaded with major %d (cap=%d)\n", major, cap);
	return 0;
}

static void __exit mychardev_exit(void)
{
	dev_t dev = MKDEV(major, 0);
	/* delete the char device */
	cdev_del(&my_cdev);
	unregister_chrdev_region(dev, 1);

	printk(KERN_INFO "mychardev: unloaded\n");
}


module_init(mychardev_init);
module_exit(mychardev_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("JIE");
MODULE_DESCRIPTION("Character device driver with uppercase conversion option");






































